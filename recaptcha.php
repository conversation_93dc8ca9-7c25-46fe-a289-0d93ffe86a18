<?php
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $secret = "6LfTVg8qAAAAAJZn3E2chPf2ciG_ujC-po-jUMD7";
    $response = $_POST["g-recaptcha-response"];
    $remoteip = $_SERVER["REMOTE_ADDR"];

    $url = "https://www.google.com/recaptcha/api/siteverify";
    $data = [
        'secret' => $secret,
        'response' => $response,
        'remoteip' => $remoteip
    ];

    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        ]
    ];

    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    $resultJson = json_decode($result);

    if ($resultJson->success !== true) {
        echo "CAPTCHA vérification échouée. Veuillez réessayer.";
    } else {
        // Traitement du formulaire ici
        echo "Formulaire soumis avec succès.";
    }
}
?>
