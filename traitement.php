<?php
$nom = htmlspecialchars($_POST['nom']);
$prenom = htmlspecialchars($_POST['prenom']);
$email = htmlspecialchars($_POST['email']);
$message = htmlspecialchars($_POST['message']);

$email_body = "Nom: $nom\nPrenom: $prenom\nEmail: $email\nMessage: $message\n";

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require 'PHPMailer/src/Exception.php';
require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';

$mail = new PHPMailer(true);

try {
    // Paramètres du serveur
    $mail->isSMTP();
    $mail->Host = 'smtp.gmail.com';
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>'; 
    $mail->Password = 'rytu bysi ioez izxx'; 
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
    $mail->Port = 465;

    // Destinataires
    $mail->setFrom('<EMAIL>', 'Client-dtn');
    $mail->addAddress('<EMAIL>'); 

    // Contenu de l'email
    $mail->isHTML(false);
    $mail->Subject = "Message d'un client";
    $mail->Body = $email_body;

    // Envoi de l'email
    $mail->send();
    echo '<script>
            alert("Message envoyé avec succès !");
            window.location.href = "contact.html";
          </script>';
} catch (Exception $e) {
    echo '<script>
            alert("Le message n\'a pas pu être envoyé. Erreur : ' . $mail->ErrorInfo . '");
            window.history.back();
          </script>';
}
?>
