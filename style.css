/* General responsive improvements */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    margin: 0;
    padding: 0;
    background-color: #ffffff;
    overflow-x: hidden;
}

img {
    max-width: 100%;
    height: auto;
}

/* Styles généraux */
*{
   
    font-family: 'Montserrat', 'Odoo Unicode Support Noto', sans-serif;
}


html{scroll-behavior: smooth;}
body {
    margin: 0;
    padding: 0;
    background-color: #ffffff;
   
}
h4{
    color: #000000ba;
    font-weight: 100;
    font-style: normal;
}
p{
     font-family: 'Montserrat', 'Odoo Unicode Support Noto', sans-serif;
}

/* Styles de base de la barre de navigation */
.nav {
    width: 100%;
    height: 90px;
    background-color: #ffffff;
    color: #000000;
    display: flex;
    align-items: center;
    padding: 0 5px;
    position: fixed;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(99, 98, 98, 0.2);
    font-family: 'Montserrat', 'Odoo Unicode Support Noto', sans-serif;
    transition: height 0.4s ease, background-color 0.3s ease, box-shadow 0.3s ease;
}

.nav.scrolled {
    height: 70px; 
    background-color: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); 
}
.nav img {
    height: 50px;
    border: none;
    border-radius: 12px;
    margin-left: 20px;
    display: block;
    transition: height 0.5s ease; 
}
.nav a {
    display: block; /* Assure que le lien occupe toute la largeur nécessaire */
}

.nav.scrolled img {
    height: 40px; 
}

.menu-icon {
    display: none;
    cursor: pointer;
}

.nav h2 {
    margin: 10px;
    width: 25%;
    font-size: 24px;
    padding: 10px 4px;
    border-radius: 1px;
    font-weight: 700; /* Bold */
}

.lien-page {
    height: 70px;
    width: 70%;
    display: flex;
    justify-content: center;
    text-align: center;
    margin-left: 120px;
    margin-right: 50px;
    
}

.lien {
    list-style-type: none;
    padding: 0;
    display: flex;
    width: auto;
    justify-content: space-between;
    align-items: center;
    text-align: center;
}

.lien > li {
    margin-top: 10px;
    position: relative;
    width: 30%;
    height: 25px;
    font-weight: bold;
    font-size: 15px;
    margin: 0 15px;
}

.lien > li a {
    text-decoration: none;
    color: #000000;
    transition: color 0.2s ease;
    display: flex;
    padding-left: 10px;
    font-family: 'Montserrat', 'Odoo Unicode Support Noto', sans-serif;
}



.specialite-svg {
    height: 20px;
    width: 20px;
    font-size: 20px;
    color: #000000;
    margin-bottom: 6px;
    margin-right: 5px;
    vertical-align: middle;
}




/* pour les pages actives */
.lien a {
    color: black;
    text-decoration: none;
    padding: 10px;
    display: inline-block;
    transition: background-color 0.3s, color 0.3s;
  }
  
  .lien a.active {
    color: white ;
    background-color:#1592ffa7 ; 
    border-radius: 20px ;
    padding: 10px 15px ; 
    transition: background-color 0.1s, color 0.1s;
  }
  
  
  










/* drop down */
.dropdown-content {
    display: none;
    list-style: none;
    position: absolute;
    background-color: #f9f9f9;
    width: 200px;
    border: 0,5px solid rgba(128, 128, 128, 0.653);
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.1);
    z-index: 1;
    padding: 0; 
    margin: 0; 
    padding-top: 10px;
}

.dropdown-content li {
    width: 100%;
    margin: 0; 
    padding: 0; 
}

.dropdown-content li a {
    color: black;
    padding: 10px 16px; 
    text-decoration: none;
    display: block;
    text-align: left;
}
.dropdown:hover .dropdown-content {
    display: block;
}
.dropdown svg {
    margin-left: 5px;
    vertical-align: middle;
    transition: fill 0.2s ease;
}

.dropdown-content a:hover {
    color: #317ed6;
    
}

.tel {
    border-left: 1px solid rgba(142, 131, 131, 0.358);
    padding-left: 15px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    height: 100%;
    width: 25%;
    background-color: #fff;  
    transition: box-shadow 0.3s ease;
    
}

.tel p {
    margin: 5px 0;
    font-size: 16px;
    color: #000000;
    font-weight: bold;
}

.tel-edit {
    display: flex;
    align-items: center;
}

.tel-edit >svg {
    height: 18px;
    width: 18px;
    margin-right: 10px;
    fill: #000000;
}

.tel:hover {
    background-color: rgba(240, 239, 239, 0.498);
    cursor: auto;
}


.rendez-vous {
    background-color: #1592ffc5;
    color: #ffffff;
    width: 32%;
    height: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
    cursor: pointer;
}

.rendez-vous:hover {
    background-color: #0088ff8f;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); 
}

.rendez-vous svg {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}

.rendez-vous p {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
}


/* hero*/
.hero{
    display: flex;
    margin-top: 15%;
    width: 100%;
    justify-content: space-between;
    height: 50%;
}
.hero-text{
    width: 45%;
    padding: 0 10px;
    margin-left: 5%;
    
}
.hero-text >h2{
    font-size:60px;
    font-family: "Julius Sans One", sans-serif;
    text-align: center;
    margin-top: 5px;
}
.heroh3{
    margin-left: 80px;
    font-size: 23px;
    position: static;
    color: #317ed695;
    margin-bottom: 6px;
}
.hero-text h4{
    font-size: 22px;
    text-align: center;
    
}
.hero-text p {
    font-size: 18px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 400;
    color: #34495e;
    line-height: 1.8;
    margin: 0 auto;
    max-width: 80%;
}
.hero-img{
    width: 40%;
    margin-right: 50px;
}
.hero-img >img{
    border-radius: 16px;
    width: 90%;
}

/* carousel*/
.carousel-cont {
    width: 90%;
    margin-top: 18%;
    margin-bottom: 20%;
    margin-left: auto;
    margin-right: auto;
}

.carousel-cont h2 {
    text-align: center;
    font-size: 30px;
    color: #3b89d6; 
}

.carousel {
    position: relative;
    width: 100%;
    height: 450px;
    overflow: hidden;
    background-color: #ffffff;
    margin: 50px auto 0;
}

.carousel-inner {
    display: flex;
    transition: transform 0.5s ease;
}

.carousel-item {
    display: flex;
    box-sizing: border-box;
    
}

.carousel-item img {
    width: calc(33.33% - 30px); /* Adjust width to account for padding */
    height: 450px; 
    box-sizing: border-box;
    margin: 0 20px;
    margin-left: 100px;
}

.carousel-control {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    padding: 10px;
    cursor: pointer;
}

.carousel-control.prev {
    left: 0;
    margin-left: 15px;
}

.carousel-control.next {
    right: 0;
    margin-right: 15px;
}

.carousel button {
    width: 50px;
    height: 50px;
    border-radius: 30px;
    background-color: rgb(169, 168, 168);
}

.carousel button:hover {
    background-color: rgb(220, 219, 217);
}
@media only screen and (min-width: 320px) and (max-width: 1024px) {
    .carousel-cont {
      margin-top: 8%;
      margin-bottom: 10%;
    }
  
    .carousel-cont h2 {
      font-size: 24px;
    }
  
    .carousel {
      height: 300px; /* Hauteur ajustée pour le mobile */
    }
  
    .carousel-item img {
      width: calc(50% - 20px); /* Afficher deux images par vue sur mobile */
      height: 300px;
      margin: 0 10px;
    }
  
    .carousel-control {
      padding: 8px;
      font-size: 18px;
    }
  
    .carousel button {
      width: 40px;
      height: 40px;
    }
  }






/* specialite all  */
.cont-spec{
    background-color: #fffefe;
    margin-top: 83px;
    height: 135px;
    
}
.specialite{
    width: 76%;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    
}
.box {
    padding-top: 20px;
    height: 110px;
    width: 184px;
    text-align: center;
    transition: all 0.45s;
    
}
.box a {
    text-decoration: none;
    color: black;
}
.box svg{
    height: 50px;
}
.box:hover{
    border:0,1px solid rgb(123, 122, 122);
    border-radius: 12px;
    background: rgb(247, 247, 247);
    transition: background-color 0.3s ease;
}
/* Styles pour la version mobile */
@media only screen and (min-width: 320px) and (max-width: 1024px){
    .cont-spec {
        display: none;
        flex-wrap: wrap;
        justify-content: center;
        margin-bottom: 10%;
        height: 100%;
        width: 100%;
        margin-left: auto;
        margin-right: auto;
    }

    .specialite {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }

    .box {
        flex: 0 0 10%; 
        margin: 10px;
        text-align: center;
    }

    .box svg {
        width: 50px; 
        height: 50px;
    }

    .box p {
        font-size: 13px; 
        margin-top: 10px;
    }
}



/* specialite hero*/
.spec-hero {
    display: flex;
    margin-top: 15%;
    width: 100%;
    justify-content: space-between;
}

.spec-hero-text {
    width: 45%;
    padding: 0 10px;
    margin-left: 5%;
}


.spec-hero-text h2 {
    font-size: 60px;
    text-align: center;
    font-family: "Julius Sans One", sans-serif;
}

.spec-hero-text h4 {
    font-size: 24px;
    text-align: center;
    font-weight: 100;
    font-style: normal;
}

.spec-hero-text p {
    font-size: 20px;
    text-align: center;
    color: #405a74;
    line-height: 1.6;
    
}



/* spec all boxes */
.spec-all-cont{
    width: 90%;
    margin-top: 15%;
    margin-bottom: 10%;
    height: 650px;
    background-color: #f9f8f8;
    margin-left: auto;
    margin-right: auto;
    border-radius: 12px;
    padding: 20px;
}

.specialite-top{
    float: left;
    display: flex;
    width: 100%;
    height: 250px;
    margin-left: auto;
    margin-right: auto;
    justify-content: center;
    align-items: center;
}
.grand-box {
    padding-top: 35px;
    height: 200px;
    width: 300px;
    text-align: center;
    transition: all 0.45s;
}
.grand-box a {
    text-decoration: none;
    color: black;
    
}
.grand-box svg{
    height: 80px;
}
.grand-box p{
    font-size: 22px;
    font-weight: 100;
    font-style: normal;
}
.grand-box:hover{
    border:0,1px solid rgb(123, 122, 122);
    border-radius: 12px;
    background: rgb(240, 239, 239);
    transition: background-color 0.3s ease;
}
.spec-all-cont h1{
    text-align: center;
    font-size: 35px;
    color: rgb(0, 0, 0);
    font-family: 'Montserrat', 'Odoo Unicode Support Noto', sans-serif;
    font-weight: 100;
    font-style: normal;
}
.spec-all-cont h2{
    text-align: center;
    font-size: 30px;
    color: rgb(59, 137, 214);
}


/* descr du trait*/
.dental-section {
    padding: 60px;
    margin-top: 2%;
    background: #ffffff9e;
    color: #333;
}

.content-wrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: 50px;
    gap: 30px;
    align-items: center;
}

.text-content {
    flex: 1.5;
}

.text-content h2 {
    font-size: 40px;
    color: #2c3e50;
    margin-bottom: 20px;
}

.text-content p {
    font-size: 20px;
    line-height: 1.6;
    margin-bottom: 30px;
    color: #666;
}
.key-points{
    margin-top: 7%;
}
.key-points h3 {
    font-size: 28px;
    margin-bottom: 15px;
    color: #3498db;
}

.key-points ul {
    list-style: none;
    padding: 0;
}

.key-points li {
    font-size: 18px;
    margin-bottom: 15px;
    display: flex;
    align-items: baseline;
}

.key-points li span {
    font-weight: bold;
    margin-right: 8px;
    color: #2c3e50;
}

.image-content {
    flex: 1;
    text-align: center;
}

.image-content img {
    width: 100%;
    max-width: 700px;
    border-radius: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}
.treatment-section {
    padding: 40px;
    border-radius: 10px;
    margin: 40px auto;
    margin-top: 6%;
    max-width: 1200px;
}

.treatment-section h3 {
    font-size: 28px;
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
}

.treatment-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.treatment-item {
    flex: 1 1 30%;
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.treatment-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.treatment-item img {
    width: 100%;
    height: auto;
    border-bottom: 3px solid #3498db;
}

.treatment-info {
    padding: 20px;
}

.treatment-info h4 {
    font-size: 24px;
    color: #2c3e50;
    margin-bottom: 10px;
}

.treatment-info p {
    font-size: 16px;
    color: #7f8c8d;
    line-height: 1.6;
}



@media (max-width: 768px) {
    .content-wrapper {
        flex-direction: column;
    }
    
    .text-content {
        margin-bottom: 15px;
    }
    
    .image-content img {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
    }
    .treatment-item{
        
    }
    .treatment-list {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .text-content h2 {
        font-size: 1.5rem;
    }

    .text-content p,
    .treatment-info p {
        font-size: 0.875rem;
    }

    .key-points ul,
    .treatment-list {
        padding: 0;
    }

    .key-points li {
        font-size: 0.875rem;
    }

    .treatment-item {
        padding: 10px;
    }

    .treatment-item img {
        max-width: 100%;
        height: auto;
    }
}


/* foire aux qst */
.faq-section {
    max-width: 50%;
    margin: 0 auto;
    background-color: #ffffffde;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 10%;
    margin-top: 5%;
}

.span-cont {
    height: 25px;
    width: 25px;
    text-align: center;
    border-radius: 60px;
    font-size: 20px;
    display: flex; 
    align-items: center; 
    justify-content: center;
    background-color: #1592ffc3;
    color: rgb(255, 255, 255);
}

.span-cont:hover {
    background-color: #3b89d6;
    color: white;
}

h2 {
    text-align: center;
    margin-bottom: 20px;
    color: #3b89d6;
}

.faq-item {
    border-bottom: 1px solid #ddd;
    padding: 10px 0;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    font-weight: bold;
    color: #555;
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.6s ease-out;
    color: #333;
}

.faq-item.open .faq-answer {
    max-height: 120px; 
}

.faq-toggle {
    font-size: 18px;
    line-height: 18px;
    transition: transform 0.6s;
}

@media only screen and (min-width: 320px) and (max-width: 1024px){
    .faq-section {
        max-width: 80%; 
        padding: 15px;
        margin-top: 10%;
    }

    h2 {
        font-size: 1.5rem;
        margin-bottom: 15px;
    }

    .faq-question {
        font-size: 1rem; 
    }

    .span-cont {
        height: 20px;
        width: 20px;
        font-size: 18px; 
    }

    .faq-answer {
        font-size: 0.9rem; 
    }

    .faq-item.open .faq-answer {
        max-height: 150px;
    }

    .faq-toggle {
        font-size: 16px; 
    }
}



/* contact page */
.g-recaptcha {
    display: flex;
    justify-content: center;
}
.contct{
    display: flex;
    margin-top: 15%;
    width: 100%;
    justify-content: space-between;
}
.contct-text{
    width: 45%;
    padding: 0 10px;
    margin-left: 5%;
}
.contct-text >h2{
    font-size:60px;
    font-family: "Julius Sans One", sans-serif;
    text-align: center;
}
.contct-text h4{
    font-size: 44px;
    margin-top: 8px;
    text-align: center;
    color: #317ed695;
}
.contct-text p {
    font-size: 22px;
    text-align: center;
    color: #34495e;
    line-height: 1.6;
    font-weight: 100;
    font-style: normal;
    
}



.contct-input {
    background-color: #ffffff;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    width: 35%;

    max-height: 700px;
    box-sizing: border-box;
    margin-right: 60px;
}



label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

input[type="text"],
input[type="email"],
textarea {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
    color: #333;
    background-color: #f9f9f9;
    transition: border-color 0.3s;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="text"]:focus ,
input[type="tel"]:focus ,
textarea:focus{
    border-color: #007BFF;
    outline: none;
}

textarea {
    height: 120px;
    resize: none;
}

input[type="submit"] {
    width: 100%;
    padding: 10px;
    background-color:#1592ffc3;
    border: none;
    border-radius: 4px;
    color: #fff;
    font-size: 14px;
    font-weight: 550;
    cursor: pointer;
    transition: background-color 0.3s;
    margin-top: 15px;
}

input[type="submit"]:hover {
    background-color: #137ff2e6;
}
/* contact info */
.contact-info {
    margin-top: 7%;
    width: 80%;
    margin-bottom: 20px;
    margin-right: auto;
    margin-left: auto;
  }
  .contact-info h2{
    font-size: 24px;
    font-family: 'Montserrat', 'Odoo Unicode Support Noto', sans-serif;
    margin-bottom: 5%;
  }
  .contact-info p {
    margin: 5px 0;
    font-size: 18px;
  }
  .contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
  }
  
  .contact-item svg {
    margin-right: 0.75rem;
    height: 20px;
    width: 20px;
  }
  .map-container {
    position: relative;
    width: 100%;
    margin: 0 auto;
    padding: 20px;
    box-sizing: border-box;
  }
  
  .map {
    width: 82%;
    height: 400px; 
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin-right: auto;
    margin-left: auto;
  }
  
  .map iframe {
    width: 100%;
    height: 100%;
    border: 0;
  }
  @media only screen and (min-width: 320px) and (max-width: 1024px) {
    .contact-info {
      width: 90%;
      margin-top: 10%;
    }
  
    .contact-info h2 {
      font-size: 18px;
      margin-bottom: 10%;
    }
  
    .contact-info p {
      font-size: 14px;
    }
  
    .contact-item {
      margin-bottom: 1.5rem;
      flex-wrap: wrap;
    }
  
    .contact-item svg {
      margin-right: 0.5rem;
      height: 14px;
      width: 14px;
    }
  }
  

/* Fenêtre modale du rendez vous */

.modal {
    display: none; 
    position: fixed; 
    z-index: 1000; 
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto; 
    background-color: rgba(0, 0, 0, 0.4); 
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto; 
    padding: 20px;
    border: 1px solid #888;
    width: 80%; /* Largeur de la modale */
    max-width: 500px; /* Largeur maximale */
    border-radius: 8px;
}

.close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close-btn:hover,
.close-btn:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.modal-content h2 {
    margin-top: 0;
    color:#3b89d6;
    text-align: center;
    font-family: 'Montserrat', 'Odoo Unicode Support Noto', sans-serif;
}

.modal-content form {
    display: flex;
    flex-direction: column;
}

.modal-content label {
    margin-top: 10px;
}

.modal-content input,
.modal-content select,
.modal-content textarea,
.modal-content button {
    width: 100%;
    padding: 10px;
    margin-top: 5px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}
.modal-content input[type="tel"],select{
    background-color: #f9f9f9;
}
.modal-content button {
    background-color: #1592ff;
    color: white;
    border: none;
    cursor: pointer;
}

.modal-content button:hover {
    background-color: #0b7dda;
}
.modal-content select:focus {
    border-color:  #007BFF; 
    outline: none;
}

.modal-content textarea:focus{
    border-color:  #007BFF; 
    outline: none;
}

/* Footer styles */
footer {
    margin-top: 10%;
    background-color: #161623; /* Couleur de fond que tu avais déjà choisie */
    color: #ffffff;
    padding: 40px 20px;
}

.footer-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
}

.footer-column {
    flex: 1;
    margin: 20px;
    min-width: 220px; 
}

.footer-about {
    flex: 2; /* Augmente la flexibilité de la colonne "À propos de nous" */
    min-width: 300px; /* Ajuste la largeur minimale pour la colonne "À propos de nous" */
}

.footer-column h3 {
    font-size: 1.2em;
    margin-bottom: 20px;
    text-transform: uppercase;
    display: inline-block;
    padding-bottom: 5px;
}

.footer-column p,
.footer-column a {
    font-size: 1em;
    margin: 10px 0;
    color: #ffffff;
    text-decoration: none;
}

.footer-column p svg,
.footer-column a svg {
    margin-right: 8px;
    vertical-align: middle;
}

.footer-column a:hover {
    color: #ffffff;  
}

.footer-links ul {
    list-style: none;
    padding: 0;
}

.footer-links ul li {
    margin: 10px 0;
}

.footer-links ul li a {
    display: flex;
    align-items: center;
}

.social-icons {
    display: flex;
    margin-top: 10px;
}

.social-icons a {
    margin-right: 15px;
    transition: transform 0.3s ease;
}

.social-icons a:hover {
    transform: scale(1.2); 
}

.social-icons a svg {
    width: 1.5em;
    height: 1.5em;
}

.footer-bottom {
    text-align: center;
    margin-top: 10px;
    border-top: 1px solid #262626;
    color: #ffffff;
    height: 25px;
    background-color:#161623;
}

/* Responsive footer styles */
@media (max-width: 768px) {
    .footer-container {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .footer-column {
        margin: 20px 0;
    }

    .social-icons {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .footer-column {
        min-width: 100%; 
    }

    .footer-column p,
    .footer-column a {
        font-size: 0.9em;
    }
}

  

/* Styles pour la barre de navigation mobile */
.nav-mobile {
    width: 100%;
    height: 60px;
    background-color: #ffffff;
    color: #000000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    position: fixed;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(99, 98, 98, 0.2);
    font-family: 'Montserrat', 'Odoo Unicode Support Noto', sans-serif;
}

.nav-mb-logo {
    display: flex;
    justify-content: center;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.nav-mobile img {
    height: 50px;
    width: 50px;
    border-radius: 12px;
    border: 2px solid white;
}

.nav-mobile svg {
    width: 40px;
    cursor: pointer;
    margin-right: 15px;
}


/* Sidebar initial state - hidden */
.sidebar {
    display: none;
    position: fixed;
    flex-direction: column;
    top: 0;
    left: 0;
    width: 240px;
    height: 100%;
    border-bottom-right-radius: 10px;
    background-color: #ffffff;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.5s ease;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    
}


.sidebar.open {
    transform: translateX(0);
}

.sidebar .close-btn {
    display: block;
    background: none;
    border: none;
    font-size: 45px;
    cursor: pointer;
    text-align: center;
    color: #333333;
    margin-left: auto;
}

.sidebar ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    flex: 1;
    align-items: center; 
    font-family: 'Montserrat', 'Odoo Unicode Support Noto', sans-serif;
}

.sidebar ul li {
    margin-bottom: 15px;
}

.sidebar ul li a {
    text-decoration: none;
    color: #333333;
    font-size: 18px;
    display: flex;
    align-items: center;
    text-align: center; 
    justify-content: center;
    padding: 10px;
    border-radius: 8px;
    transition: background-color 0.3s ease, color 0.3s ease;
    font-family: 'Montserrat', 'Odoo Unicode Support Noto', sans-serif;
}

.sidebar ul li a svg {
    margin-right: 5px;
    width: 24px;
    height: 24px;
    fill: #848383;
}



.sidebar ul .dropdown-content {
    display: none;
    flex-direction: column;
    padding-left: 15px;
}

.sidebar ul .dropdown:hover .dropdown-content {
    display: flex;
}

.sidebar ul .rendez-vous2 {
    background-color: #1592ffc9;
    color: #ffffff;
    text-align: center;
    padding: 10px 15px;
    border-radius: 12px;
    transition: background-color 0.3s ease, color 0.3s ease;
}
.rendez-vous2 svg {
    fill: white !important;
  }
.unique-dropdown-content {
    display: none;
    list-style: none;
    padding: 0;
    border: 1px solid rgba(234, 231, 231, 0.543);
}

.unique-dropdown-content.show {
    display: block;
}

.unique-dropdown-content li {
    padding: 1px;
}

.menu-toggle {
    cursor: pointer;
}

.unique-phone {
     margin-bottom: 10%;
    background-color: #ffffff; 
    padding: 20px; 
    text-align: center; 
}

.unique-phone p {
    font-size: 20px; /* Taille de police plus grande */
    color: #000000; 
    font-weight: 400; /* Texte en gras */
    margin: 0; 
}



/* Styles pour la navigation sur mobile */
@media only screen and (min-width: 320px) and (max-width: 1024px) {
    .nav {
        flex-direction: column;
        display: none;
        height: auto;
        padding: 10px;
        width: 100%;
        height: 60px;
        align-items: flex-start;
    }

    .nav h2 {
        display: none;
    }

    .lien-page {
        display: none;
        flex-direction: column;
        width: 100%;
    }

    .lien > li {
        width: 100%;
        margin: 5px 0;
        text-align: left;
    }

    .lien > li a {
        padding: 10px;
        width: 100%;
    }

    .tel, .rendez-vous {
        width: 100%;
        height: auto;
        margin-top: 10px;
        text-align: left;
        padding-left: 0;
        display: none;
    }

    .tel {
        border: none;
        padding: 10px 0;
    }

    .rendez-vous {
        margin-top: 0;
        padding: 10px;
    }

    .dropdown-content {
        position: static;
        box-shadow: none;
        background-color: transparent;
    }

    .dropdown-content li a {
        padding: 10px 20px;
    }

    .dropdown:hover .dropdown-content {
        display: none;
    }

    .dropdown-content {
        display: none;
        width: 100%;
    }

    .dropdown-content li {
        width: 100%;
    }

    .menu-toggle.open ~ .lien-page {
        display: flex;
    }
}


/* Hero */
@media only screen and (min-width: 320px) and (max-width: 1024px){
    .hero {
        flex-direction: column;
        align-items: center;
        margin-top: 20%;
    }
    .hero-text {
        width: 90%;
        padding: 0 10px;
        text-align: center;
    }
    .hero-text > h2 {
        font-size: 36px;
    }
    .hero-text h4 {
        font-size: 18px;
    }
    .hero-text p {
        font-size: 16px;
    }
    .hero-img {
        width: 90%;
        margin: 20px 0;
    }
    .hero-img > img {
        width: 100%;
        border-radius: 16px;
    }
    .heroh3{
        margin-right: 140px;
        font-size: 20px;
        position: static;
        color: #317ed695;
        margin-bottom: 6px;
    }
}





@media only screen and (min-width: 320px) and (max-width: 1024px) {
    .contct {
        flex-direction: column;
        align-items: center;
        margin-top: 10%;
    }
    
    .contct-text {
        width: 90%;
        margin: 0 auto;
        text-align: center;
    }
    
    .contct-text > h2 {
        font-size: 36px;
    }
    
    .contct-text h4 {
        font-size: 20px;
    }
    
    .contct-text p {
        font-size: 16px;
    }

    .contct-input {
        width: 90%;
        margin: 20px auto;
        padding: 20px;
    }

    
    label {
        font-size: 14px;
    }

    input[type="text"],
    input[type="email"],
    textarea {
        font-size: 14px;
    }

    input[type="submit"] {
        font-size: 14px;
        padding: 8px;
    }
    
}
@media (max-width: 768px) {
    .spec-hero {
        flex-direction: column;
        align-items: center;
        margin-top: 20%;
    }
    .spec-hero-text {
        width: 90%;
        padding: 0 10px;
        text-align: center;
    }
    .spec-hero-text > h2 {
        font-size: 36px;
    }
    .spec-hero-text h4 {
        font-size: 18px;
    }
    .spec-hero-text p {
        font-size: 16px;
    }
    .spec-hero-img {
        width: 90%;
        margin: 20px 0;
    }
    .spec-hero-img > img {
        width: 100%;
        border-radius: 16px;
    }

    .spec-all-cont {
        padding: 20px;
        margin-bottom: 30%;
        background-color: #f5f5f5;
        height: 500px;
        width: 85%;
    }
    
    .spec-all-cont h2 {
        font-size: 24px;
        text-align: center;
        margin-bottom: 10px;
    }
    
    .spec-all-cont h1 {
        font-size: 18px;
        text-align: center;
        margin-bottom: 20px;
    }
    
    .specialite-top {
        display: flex;
        height: 40%;
    }
    .specialite-bot {
        float: right;
        justify-content: space-around;
    }
    
    .grand-box {
        width: 45%;
        margin-bottom: 40px;
        text-align: center;
    }
    
    .grand-box a {
        display: block;
        text-decoration: none;
        color: #333;
    }
    
    .grand-box svg {
        width: 75px;
        height: 75px;
        margin: 0 auto 10px;
    }
    
    .grand-box p {
        font-size: 10px;
        font-weight: bold;
        text-transform: uppercase;
        margin: 0;
    }
    
}






#scrollToTopBtn {
    display: none; 
    position: fixed;
    bottom: 20px;
    text-align: center;
    justify-content: center;
    right: 30px;
    z-index: 99; /* S'assurer que le bouton est au-dessus des autres éléments */
    border: none;
    outline: none;
    background-color: #f4eeee;
    color: rgb(0, 0, 0);
    cursor: pointer;
    padding: 0;
    border-radius: 25px;
    height: 50px;
    width: 50px;
}


#scrollToTopBtn:hover {
    background-color: #c9cbcd9c;
}
