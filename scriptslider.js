let currentIndex = 0;

function showSlides(index) {
    const slides = document.querySelectorAll('.carousel-item img');
    const totalSlides = slides.length;
    const visibleSlides = 3; // Number of visible slides
    const slideWidth = 33.33 + 0.67; // 33.33% width + 0.67% for the margins

    // Calculate the new index in a circular manner
    currentIndex = (index + totalSlides) % totalSlides;

    // Update the transform property of the carousel-inner
    const carouselInner = document.querySelector('.carousel-inner');
    carouselInner.style.transform = `translateX(-${currentIndex * slideWidth}%)`;
}

function prevSlide() {
    showSlides(currentIndex - 1);
}

function nextSlide() {
    showSlides(currentIndex + 1);
}

document.querySelector('.prev').addEventListener('click', prevSlide);
document.querySelector('.next').addEventListener('click', nextSlide);
