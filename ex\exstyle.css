/* Styles généraux */
body {
    font-family:serif;
    margin: 0;
    padding: 0;
    background-color: #f0f0f0;
}

/* Barre de navigation */
.nav {
    width: 100%;
    height: 75px;
    background-color: #ffffff;
    color: #000000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    position: fixed;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav h2 {
    margin: 0;
    font-size: 24px;
    padding: 10px 0;
}

.lien {
    list-style-type: none;
    margin: 0;
    padding: 0;
    display: flex;
    margin-right: 45px;
}

.lien li {
    margin-left: 25px;
    position: relative;
}

.lien li a {
    text-decoration: none;
    color: #000000;
    font-size: 18px;
    transition: color 0.2s ease;
}

.lien li a:hover {
    color: #7b787b;
}
#tel >a{
    border-radius: 8px;
    color: #ffffff;
    background-color: #000000;
    padding: 6px;
}
#tel >a:hover{
    background-color: #000000c4;
}
/* Dropdown styles */
.dropdown-content {
    display: none;
    position: absolute;
    background-color: #ffffff;
    min-width: 160px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    z-index: 1;
    margin-top: 10px;
    padding: 0;
    border-radius: 8px;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.4s ease;
}

.dropdown-content.show {
    display: block;
    opacity: 1;
    pointer-events: auto;
}

.dropdown-content li {
    padding: 10px;
    list-style-type: none;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.dropdown-content li a {
    color: #000;
    text-decoration: none;
}

.dropdown-content li:hover {
    background-color: #b5b0b050;
}

/* Container */
.container {
    position: relative;
    display: flex;
    flex-direction: column; 
    justify-content: flex-start;
    align-items: center; 
    max-width: 100%;
    height: 30%;
    margin: 100px;
    margin-top: 20%;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Info */
.info {
    position: relative;
    z-index: 10; 
    width: 22%;
    height: 100%;
    margin-right: 75.8%;
    padding: 20px;
    background-color: #2c3e508c;
    color: #fff;
    border-left:2px ;
    border-top-left-radius: 15px;
    border-bottom-left-radius: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.info h2 {
    margin-top: 0;
    font-size: 20px;
}

.hours {
    display: flex;
    flex-direction: column;
}

.day-time {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.day,
.time {
    flex: 1;
    padding: 5px 0;
    text-align: center;
    border-bottom: 1px solid white;
}

.day {
    text-transform: uppercase;
}

/* Map container */
.map-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff00; 
    border-radius: 15px;
    overflow: hidden;
}

.map-container .map {
    width: 80%;
    height: 100%;
    margin-left: 25%;
}

.map iframe {
    width: 100%;
    height: 100%; 
    border: none;
    
}

























/* Contact */
.contact {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    max-width: 80%;
    margin: 100px auto;
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.contact h2 {
    color: #333;
    font-size: 24px;
    margin-bottom: 15px;
}

.contact-form {
    width: 55%;
    max-height: 500px;
    margin-right: 20px;
}

.contact label {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 16px;
}

.contact input[type="text"],
.contact input[type="email"],
.contact textarea {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #dacfcf;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
}

.contact input[type="text"]:focus,
.contact input[type="email"]:focus,
.contact textarea:focus {
    outline: none;
    border-color: #555;
}

.contact textarea {
    height: 100px;
    overflow: hidden;
}

.contact img {
    margin-top: 30px;
    height: auto;
    max-width: 40%;
    border-radius: 8px;
}

.contact input[type="submit"] {
    display: block;
    width: 60%;
    height: 40px;
    margin: 0 auto;
    font-size: 16px;
    border: none;
    border-radius: 18px;
    background-color: #000;
    color: rgb(255, 255, 255);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.contact input[type="submit"]:hover {
    background-color: #000000a4;
}
