const treatmentItems = document.querySelectorAll('.treatment-item');
const treatmentDesc = document.getElementById('treatment-desc');
const treatmentImage = document.getElementById('treatment-image');

treatmentItems.forEach(item => {
    item.addEventListener('mouseover', function() {
        const desc = this.getAttribute('data-desc');
        const img = this.getAttribute('data-img');
        
        treatmentImage.style.opacity = '0';
        setTimeout(() => {
            treatmentImage.src = img;
            treatmentImage.style.opacity = '1';
        }, 300);

        treatmentDesc.textContent = desc;
    });

    item.addEventListener('mouseout', function() {
        treatmentImage.style.opacity = '0';
        setTimeout(() => {
            treatmentImage.src = 'chirurgie-modern.jpg'; // Image par défaut
            treatmentImage.style.opacity = '1';
        }, 300);

        treatmentDesc.textContent = "Passez la souris sur un traitement pour voir la description et l'image associée.";
    });
});
