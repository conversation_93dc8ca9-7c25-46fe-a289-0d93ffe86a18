/* slider photos */
.carousel-cont{
    height: 500px;
    width: 100%;
    margin-top: 10%;
    margin-bottom: 20%;
}
.carousel-cont h1{
    text-align: center;
    color: rgb(59, 137, 214);
    padding: 10px;
}
.carousel {
    position: relative;
    width: 100%;
    height: 450px;
    overflow: hidden;
    background-color: #555;
    margin: 50px auto 0;

}

.carousel-inner {
    display: flex;
    transition: transform 0.5s ease;
}

.carousel-item {
    display: flex;
    box-sizing: border-box;
}

.carousel-item img {
    width: 450px;
    height: 450px; 
    box-sizing: border-box;
}

.carousel-control {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    padding: 10px;
    cursor: pointer;
}

.carousel-control.prev {
    left: 0;
    margin-left: 15px;
}

.carousel-control.next {
    right: 0;
    margin-right: 15px;
}
.carousel button{
    width: 50px;
    height: 50px;
    border-radius: 30px;
    background-color: rgb(169, 168, 168);
}
.carousel button:hover{
    background-color: rgb(220, 219, 217);
}

/* MAP */

.container {
    position: relative;
    display: flex;
    flex-direction: column; 
    justify-content: flex-start;
    align-items: center; 
    max-width: 100%;
    height: 500px;
    margin: 100px;
    margin-top: 10%;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    
    
}

/* Info */
.info {
    position: relative;
    z-index: 10; 
    width: 22%;
    height: 100%;
    margin-right: 75.8%;
    padding: 20px;
    background-color: transparent ; 
    color: #ffffff;
    border-left:2px ;
    border-top-left-radius: 17px;
    border-bottom-left-radius: 17px;
    
}

.info h2 {
    margin-top: 0;
    font-size: 20px;
    color: white;
}
.info p{
    font-size: 16px;
    font-family: 'Roboto', sans-serif;
}

.hours {
    display: flex;
    flex-direction: column;
}

.day-time {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.day,
.time {
    flex: 1;
    padding: 1px 0;
    text-align: center;
    border-bottom: 1px solid rgb(255, 255, 255);
}

.day {
    text-transform: uppercase;
}

/* Map container */
.map-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #1592ffc3; 
    border-radius: 15px;
    overflow: hidden;
}

.map-container .map {
    width: 80%;
    height: 100%;
    margin-left: 25%;
}

.map iframe {
    width: 100%;
    height: 100%; 
    border: none;
    
}



