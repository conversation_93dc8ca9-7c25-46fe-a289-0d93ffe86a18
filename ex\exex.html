<div class="spec-hero-new">
    <div class="spec-hero-text">
        <h2>L'ODF</h2>
        <p>L'orthodontie dento-faciale (ODF) se concentre sur le diagnostic, la prévention et le traitement des anomalies de position des dents et des mâchoires. Son objectif est d'améliorer la fonction et l'esthétique du sourire.</p>
        
        <div class="spec-section">
            <h3>Pourquoi l'ODF ?</h3>
            <ul>
                <li><strong>Alignement des Dents</strong> : Corrige les dents mal alignées.</li>
                <li><strong>Sant<PERSON> Buccale</strong> : Facilite le brossage et réduit les caries.</li>
                <li><strong>Fonction Mâchoire</strong> : Améliore la mastication.</li>
                <li><strong>Confiance en Soi</strong> : Un sourire aligné renforce l'estime de soi.</li>
            </ul>
        
            <h3>Types de Traitements</h3>
            <ul>
              <li>Bagues orthodontiques en métal</li>
              <li>Diagnostic et Planification</li>
              <li>Bagues orthodontiques en céramiques</li>
              <li>Aligneurs invisibles</li>
              <li>Appareils de contention</li>
            </ul>
        
            <h3>Processus de Traitement</h3>
            <ol>
                <li>Consultation Initiale</li>
                <li>Diagnostic et Planification</li>
                <li>Traitement Actif</li>
                <li>Phase de Contention</li>
            </ol>
        </div>
    </div>
    <div class="spec-hero-img">
        <img src="odf.jpg" alt="Orthodontie dento-faciale">
    </div>
</div>
.spec-hero-new {
    display: flex;
    margin-top: 5%;
    width: 100%;
    justify-content: space-between;
}

.spec-hero-text p,
.spec-hero-text ul,
.spec-hero-text ol {
    color: #34495e;
    line-height: 1.6;
}

.spec-hero-text ul,
.spec-hero-text ol {
    margin: 10px 0;
    padding-left: 20px;
    list-style: none; /* Enlever le style de liste par défaut */
}

.spec-hero-text li {
    margin-bottom: 10px;
    position: relative; /* Pour positionner les marqueurs manuellement */
    padding-left: 20px; /* Espace pour le marqueur */
}

.spec-hero-text li::before {
    content: "•";
    color: #3498db;
    font-weight: bold;
    position: absolute;
    left: 0; /* Rapprocher le marqueur du texte */
    top: 0;
    font-size: 1.2em; /* Agrandir le point */
}

.spec-section h3 {
    font-size: 24px;
    color: #3b89d6
}

.spec-hero-img {
  width: 200px;
  height: 300px;
  border: 2px solid 2px ;
  margin-right:500px ;
}
.spec-hero-img >img{
    width: 500px;
  height: 400px;
  
}