<?php
use P<PERSON>Mailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require 'PHPMailer/src/Exception.php';
require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';

    $nom = htmlspecialchars($_POST['nom']);
    $prenom = htmlspecialchars($_POST['prenom']);
    $email = htmlspecialchars($_POST['email']);
    $tel = htmlspecialchars($_POST['tel']);
    $preference = htmlspecialchars($_POST['preference']);
    $prestation = htmlspecialchars($_POST['prestation']);
    $message = htmlspecialchars($_POST['message']);

    $email_body = "Nom: $nom\nPrenom: $prenom\nEmail: $email\nTéléphone: $tel\nPréférence du rendez-vous: $preference\nPrestation: $prestation\nMessage: $message\n";

    $mail = new PHPMailer(true);

    try {
        // Paramètres du serveur
        $mail->isSMTP();
        $mail->Host = 'smtp.gmail.com';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>'; 
        $mail->Password = 'rytu bysi ioez izxx'; 
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
        $mail->Port = 465;

        // Destinataires
        $mail->setFrom('<EMAIL>', 'Client-dtn');
        $mail->addAddress('<EMAIL>'); 

        // Contenu de l'email
        $mail->isHTML(false);
        $mail->Subject = "Demande de rendez-vous d'un client";
        $mail->Body = $email_body;

        // Envoi de l'email
        $mail->send();
        echo '<script>
                alert("Demande de rendez-vous envoyée avec succès !");
                window.location.href = "index.html";
              </script>';
    } catch (Exception $e) {
        echo '<script>
                alert("La demande de rendez-vous n\'a pas pu être envoyée. Erreur : ' . $mail->ErrorInfo . '");
                window.history.back();
              </script>';
    }

?>
