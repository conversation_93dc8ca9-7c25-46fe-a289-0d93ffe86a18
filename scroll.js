document.addEventListener("DOMContentLoaded", function() {
    const scrollToTopBtn = document.getElementById("scrollToTopBtn");

    // Afficher le bouton lorsque l'utilisateur fait défiler vers le bas de 20px
    window.onscroll = function() {
        if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
            scrollToTopBtn.style.display = "block";
        } else {
            scrollToTopBtn.style.display = "none";
        }
    };

    // Fonction de défilement fluide
    function smoothScrollTo(top) {
        const startY = window.scrollY || window.pageYOffset;
        const distance = top - startY;
        const startTime = performance.now();

        function step(currentTime) {
            const timeElapsed = currentTime - startTime;
            const progress = Math.min(timeElapsed / 600, 1); // durée de l'animation : 600ms
            const easeInOutQuad = progress < 0.5 ? 2 * progress * progress : -1 + (4 - 2 * progress) * progress;
            window.scrollTo(0, startY + distance * easeInOutQuad);

            if (progress < 1) {
                requestAnimationFrame(step);
            }
        }

        requestAnimationFrame(step);
    }

    // Remonter en haut de la page lorsque l'utilisateur clique sur le bouton
    scrollToTopBtn.addEventListener("click", function() {
        smoothScrollTo(0);
    });
});
