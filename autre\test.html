<div class="box-container">
    <div class="box">
        <div class="flip-card">
            <div class="flip-card-inner">
                <div class="flip-card-front">
                    <div class="front-content">
                        <img src="odf 1.jpg" alt="">
                        <div class="title">ODF</div>
                    </div>
                </div>
                <div class="flip-card-back">
                    <div class="back-content">
                        <p>decs</p>
                        <button>en savoir plus</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>










/* Box et Flip Card */
.box-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20%;  
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%; 
    height: 400px; 
    position: relative;
    color: #fff; 
    text-align: center;
}

.box {
    width: 300px;
    height: 150px;
    border-radius: 12px;
    overflow: hidden; 
    margin: auto; 
}

.flip-card {
    width: 100%;
    height: 100%;
    perspective: 1000px;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.8s;
    transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
    transform: rotateY(180deg);
}

.flip-card-front,
.flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden; 
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 16px;
}

.flip-card-front {
    background-color: rgb(255, 255, 255); 
    color: black;
}

.flip-card-back {
    background-color: rgba(205, 148, 148, 0.333); 
    transform: rotateY(180deg);
}

.front-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px; 
    box-sizing: border-box;
}

.front-content img {
    width: 300px;
    height: 150px;
}

.title {
    font-size: 2rem; 
    font-weight: bold;
    font-family:  'Source Serif Pro';
    text-align: center;
    position: absolute;
    top: 80%;
    right: 60%;
    transform: translate(-50%, -50%); /* Pour centrer le titre */
    color: #ffffff; 
    
}

.back-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 10px; /
    box-sizing:border-box; 
}
.back-content p{
    font-size: 0.9rem;
    color: rgb(255, 255, 255);
    font-family: serif;
}