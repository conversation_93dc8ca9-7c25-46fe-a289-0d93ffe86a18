function toggleFaq(element) {
    const faqItem = element.parentElement;
    const faqAnswer = faqItem.querySelector('.faq-answer');
    const toggleSymbol = faqItem.querySelector('.span-cont');
    
    if (faqItem.classList.contains('open')) {
        toggleSymbol.textContent = '+';
        faqItem.classList.remove('open');
    } else {
        toggleSymbol.textContent = '-';
        faqItem.classList.add('open');
    }
}
